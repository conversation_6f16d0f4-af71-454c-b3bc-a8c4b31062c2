# 常用模式和最佳实践

- 前端代码重构最佳实践：已完成深度重构，统一了工具函数(utils.js包含20+个函数)、错误处理机制、常量管理(CONSTANTS配置)、代码注释规范。消除了formatTime、getPlatformName、getActionText等重复函数，统一了所有uni.showToast/showModal/showLoading调用。重构涉及12个文件，消除约200行重复代码，添加80+处注释，零破坏性变更。
- base-adapter.js重构最佳实践：将复杂的executeRequest方法拆分为7个职责单一的方法：buildRequestConfig()构建请求配置、updateAuthHeaders()统一认证头更新、executeHttpRequest()执行HTTP请求、handleLoginExpired()处理登录过期、performAutoLogin()执行自动登录、retryRequestWithNewToken()重试请求、handleRequestError()统一错误处理。消除了代码重复，提高了可维护性和可测试性。
