<template>
  <responsive-layout
    :page-title="'账号管理'"
  >
    <responsive-container>
      <!-- 搜索和筛选 -->
      <view class="search-filter">
        <view class="search-box">
          <uv-input
            v-model="searchKeyword"
            placeholder="搜索游戏账号"
            border="none"
            prefixIcon="search"
            prefixIconStyle="color: #999; font-size: 32rpx;"
            :customStyle="{
              backgroundColor: '#f5f5f5',
              borderRadius: '24rpx',
              padding: '12rpx 20rpx'
            }"
            @input="onSearchInput"
          />
        </view>
        <!-- 状态筛选 -->
        <view class="filter-tabs">
          <view
            v-for="tab in filterTabs"
            :key="tab.value"
            class="filter-tab"
            :class="{ active: currentFilter === tab.value }"
            @click="onFilterChange(tab.value)"
          >
            {{ tab.label }}
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <responsive-grid
        :mobile-cols="4"
        :tablet-cols="4"
        :desktop-cols="4"
        gap="sm"
        class="stats"
      >
        <view class="stat-item grid-item">
          <text class="stat-value">{{ filteredList.length }}</text>
          <text class="stat-label">当前显示</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.active }}</text>
          <text class="stat-label">监控中</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.platforms }}</text>
          <text class="stat-label">平台数</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.shelves }}</text>
          <text class="stat-label">总货架</text>
        </view>
      </responsive-grid>

      <!-- 账号列表 -->
      <responsive-grid
        :mobile-cols="1"
        :tablet-cols="1"
        :desktop-cols="2"
        gap="base"
        class="account-list"
      >
        <view
          v-for="account in filteredList"
          :key="account.game_account"
          class="account-item grid-item clickable"
        >
          <view class="account-header">
            <view class="account-info">
              <text class="account-title">{{ account.game_account }}</text>
              <view class="account-tags">
                <text class="platform-count-tag">{{ account.platforms.length }}个平台</text>
                <text
                  class="status-tag"
                  :class="getAccountStatusClass(account)"
                >
                  {{ getAccountStatusText(account) }}
                </text>
              </view>
            </view>
          </view>

          <view class="account-details">
            <view class="platform-distribution">
              <text class="section-title">平台分布</text>
              <view class="platform-list">
                <view
                  v-for="platform in account.platforms"
                  :key="platform.platform_type"
                  class="platform-item"
                >
                  <view class="platform-info">
                    <text class="platform-name">{{ platform.platform_name }}</text>
                    <text class="shelf-count">{{ platform.shelf_count }}个货架</text>
                  </view>
                  <view class="platform-status">
                    <text
                      class="status-indicator"
                      :class="getPlatformStatusClass(platform.status_summary)"
                    >
                      {{ getPlatformStatusText(platform.status_summary) }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view class="account-footer">
            <view class="monitor-switch">
              <text class="switch-label">监控状态：</text>
              <uv-switch 
                :value="account.is_monitoring" 
                size="40" 
                @change="(value) => toggleAccountMonitor(account, value)"
              ></uv-switch>
            </view>
            <view class="account-actions">
              <uv-button
                type="warning"
                size="small"
                :loading="account.operating"
                @click="batchOffShelf(account)"
                :customStyle="{
                  borderRadius: '100px',
                  fontSize: '24rpx',
                  padding: '0 24rpx'
                }"
              >
                批量下架
              </uv-button>
            </view>
          </view>
        </view>
      </responsive-grid>

      <!-- 空状态 -->
      <view v-if="filteredList.length === 0 && !loading" class="empty-state">
        <uv-empty
          :text="emptyStateText"
          :description="emptyStateDesc"
          mode="data"
        ></uv-empty>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <uv-loading-icon mode="circle"></uv-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
    </responsive-container>
  </responsive-layout>
</template>

<script>
// {{ AURA-X: Add - 新建账号管理页面，复用货架页面设计. Approval: 寸止(ID:**********). }}
import { callFunction } from '@/utils/request'
import { formatTime, getPlatformName } from '@/utils/utils'
import ResponsiveLayout from '@/components/layout/responsive-layout.vue'
import ResponsiveContainer from '@/components/layout/responsive-container.vue'
import ResponsiveGrid from '@/components/layout/responsive-grid.vue'

export default {
  components: {
    ResponsiveLayout,
    ResponsiveContainer,
    ResponsiveGrid
  },
  name: 'AccountManagement',
  data() {
    return {
      accountList: [],
      searchKeyword: '',
      currentFilter: 'all',
      loading: false,
      currentTabIndex: 4, // 账号页面是第5个tab，索引为4
      filterTabs: [
        { label: '全部', value: 'all' },
        { label: '监控中', value: 'monitoring' },
        { label: '已停止', value: 'stopped' },
        { label: '有货架', value: 'has_shelves' }
      ]
    }
  },
  computed: {
    // 过滤后的账号列表
    filteredList() {
      let filtered = this.accountList

      // 搜索过滤
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.trim().toLowerCase()
        filtered = filtered.filter(account =>
          account.game_account.toLowerCase().includes(keyword)
        )
      }

      // 状态过滤
      if (this.currentFilter !== 'all') {
        filtered = filtered.filter(account => {
          switch (this.currentFilter) {
            case 'monitoring':
              return account.is_monitoring
            case 'stopped':
              return !account.is_monitoring
            case 'has_shelves':
              return account.total_shelves > 0
            default:
              return true
          }
        })
      }

      return filtered
    },

    // 总体统计
    totalStats() {
      const stats = {
        active: 0,
        platforms: new Set(),
        shelves: 0
      }

      this.accountList.forEach(account => {
        if (account.is_monitoring) stats.active++
        stats.shelves += account.total_shelves
        account.platforms.forEach(platform => {
          stats.platforms.add(platform.platform_type)
        })
      })

      return {
        active: stats.active,
        platforms: stats.platforms.size,
        shelves: stats.shelves
      }
    },

    emptyStateText() {
      if (this.accountList.length === 0) {
        return '暂无账号数据'
      }
      return '暂无符合条件的账号'
    },

    emptyStateDesc() {
      if (this.accountList.length === 0) {
        return '请先在货架管理中同步平台数据'
      }
      return '请尝试调整筛选条件'
    }
  },

  async onShow() {
    this.loadAccountList()
  },

  methods: {
    // 加载账号列表
    async loadAccountList() {
      this.loading = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'getAccountList'
        })

        if (result.code === 0) {
          this.accountList = result.data.map(account => ({
            ...account,
            operating: false
          }))
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载账号列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 搜索输入处理
    onSearchInput() {
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        // 触发搜索
      }, 300)
    },

    // 筛选条件变更
    onFilterChange(value) {
      this.currentFilter = value
    },

    // 获取账号状态样式类
    getAccountStatusClass(account) {
      if (!account.is_monitoring) return 'status-stopped'
      if (account.total_shelves === 0) return 'status-empty'
      return 'status-active'
    },

    // 获取账号状态文本
    getAccountStatusText(account) {
      if (!account.is_monitoring) return '已停止'
      if (account.total_shelves === 0) return '无货架'
      return '监控中'
    },

    // 获取平台状态样式类
    getPlatformStatusClass(statusSummary) {
      if (statusSummary.available > 0) return 'status-available'
      if (statusSummary.rented > 0) return 'status-rented'
      return 'status-offline'
    },

    // 获取平台状态文本
    getPlatformStatusText(statusSummary) {
      if (statusSummary.available > 0) return `${statusSummary.available}待租`
      if (statusSummary.rented > 0) return `${statusSummary.rented}出租中`
      return '已下架'
    },

    // 切换账号监控状态
    async toggleAccountMonitor(account, value) {
      try {
        const result = await callFunction('shelf-management', {
          action: 'toggleAccountMonitor',
          data: {
            gameAccount: account.game_account,
            isActive: value
          }
        })

        if (result.code === 0) {
          account.is_monitoring = value
          uni.showToast({
            title: result.message,
            icon: 'success'
          })
        } else {
          uni.showModal({
            title: '操作失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('切换监控状态失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },

    // 批量下架
    async batchOffShelf(account) {
      // 确认操作
      const confirmResult = await new Promise(resolve => {
        uni.showModal({
          title: '确认操作',
          content: `确定要下架账号"${account.game_account}"在所有平台的货架吗？`,
          success: res => resolve(res.confirm)
        })
      })

      if (!confirmResult) return

      account.operating = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'batchOffShelfByAccount',
          data: {
            gameAccount: account.game_account
          }
        })

        if (result.code === 0) {
          uni.showToast({
            title: result.message,
            icon: 'success'
          })
          // 重新加载数据
          this.loadAccountList()
        } else {
          uni.showModal({
            title: '操作失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('批量下架失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      } finally {
        account.operating = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// {{ AURA-X: Add - 复用货架页面样式，确保UI一致性. Approval: 寸止(ID:**********). }}
@import '@/common/css/responsive.scss';

.search-filter {
  margin-bottom: $spacing-base;

  .search-box {
    margin-bottom: $spacing-sm;
  }

  .filter-tabs {
    display: flex;
    gap: $spacing-xs;
    flex-wrap: wrap;

    .filter-tab {
      padding: $spacing-xs $spacing-sm;
      background: $bg-color-container;
      border-radius: $border-radius-base;
      font-size: $font-size-sm;
      color: $text-color-secondary;
      transition: all $transition-fast;
      cursor: pointer;

      &.active {
        background: $color-primary;
        color: $color-white;
      }

      &:hover {
        background: $bg-color-hover;
      }
    }
  }
}

.stats {
  margin-bottom: $spacing-base;

  .stat-item {
    text-align: center;
    padding: $spacing-base;
    background: $bg-color-container;
    border-radius: $border-radius-base;

    .stat-value {
      display: block;
      font-size: $font-size-xl;
      font-weight: bold;
      color: $color-primary;
      margin-bottom: $spacing-xs;
    }

    .stat-label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }
}

.account-list {
  .account-item {
    background: $bg-color-container;
    border-radius: $border-radius-base;
    padding: $spacing-base;
    border: 1rpx solid $border-color-light;
    transition: all $transition-fast;

    &:hover {
      border-color: $color-primary;
      box-shadow: $shadow-light;
    }

    .account-header {
      margin-bottom: $spacing-base;

      .account-info {
        .account-title {
          font-size: $font-size-lg;
          font-weight: bold;
          color: $text-color-primary;
          margin-bottom: $spacing-xs;
          display: block;
        }

        .account-tags {
          display: flex;
          gap: $spacing-xs;
          flex-wrap: wrap;

          .platform-count-tag {
            padding: 4rpx $spacing-xs;
            background: $bg-color-info;
            color: $color-info;
            font-size: $font-size-xs;
            border-radius: $border-radius-sm;
          }

          .status-tag {
            padding: 4rpx $spacing-xs;
            font-size: $font-size-xs;
            border-radius: $border-radius-sm;

            &.status-active {
              background: $bg-color-success;
              color: $color-success;
            }

            &.status-stopped {
              background: $bg-color-error;
              color: $color-error;
            }

            &.status-empty {
              background: $bg-color-warning;
              color: $color-warning;
            }
          }
        }
      }
    }

    .account-details {
      margin-bottom: $spacing-base;

      .section-title {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        margin-bottom: $spacing-xs;
        display: block;
      }

      .platform-list {
        .platform-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: $spacing-xs 0;
          border-bottom: 1rpx solid $border-color-lighter;

          &:last-child {
            border-bottom: none;
          }

          .platform-info {
            .platform-name {
              font-size: $font-size-base;
              color: $text-color-primary;
              margin-bottom: 2rpx;
              display: block;
            }

            .shelf-count {
              font-size: $font-size-xs;
              color: $text-color-secondary;
            }
          }

          .platform-status {
            .status-indicator {
              padding: 2rpx $spacing-xs;
              font-size: $font-size-xs;
              border-radius: $border-radius-sm;

              &.status-available {
                background: $bg-color-success;
                color: $color-success;
              }

              &.status-rented {
                background: $bg-color-warning;
                color: $color-warning;
              }

              &.status-offline {
                background: $bg-color-info;
                color: $color-info;
              }
            }
          }
        }
      }
    }

    .account-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .monitor-switch {
        display: flex;
        align-items: center;
        gap: $spacing-xs;

        .switch-label {
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }

      .account-actions {
        display: flex;
        gap: $spacing-xs;
      }
    }
  }
}

.empty-state,
.loading-state {
  text-align: center;
  padding: $spacing-xl 0;

  .loading-text {
    margin-top: $spacing-sm;
    color: $text-color-secondary;
    font-size: $font-size-sm;
  }
}
</style>
